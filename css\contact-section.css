/* Contact section styles */
.contact-section {
    max-width: 1200px;
    margin: 0 auto;
    padding: 40px 20px;
}

.section-header {
    margin-bottom: 40px;
    position: relative;
}

.get-in-touch {
    display: inline-block;
    background-color: rgba(39, 174, 96, 0.1);
    color: #333;
    font-size: 14px;
    font-weight: 500;
    padding: 8px 16px;
    border-radius: 30px;
    margin-bottom: 15px;
}

.get-in-touch span {
    color: #27ae60;
    border-bottom: 2px solid #27ae60;
    padding-bottom: 2px;
}

.contact-title {
    font-size: 36px;
    color: #333;
    margin-bottom: 10px;
    position: relative;
}

.contact-title::after {
    content: '';
    display: block;
    width: 80px;
    height: 4px;
    background-color: #27ae60;
    margin-top: 15px;
}

/* Contact cards container */
.contact-cards {
    display: flex;
    flex-wrap: wrap;
    gap: 30px;
    margin-top: 30px;
}

/* Contact card styles */
.contact-card {
    flex: 1;
    min-width: 300px;
    border-radius: 15px;
    overflow: hidden;
    display: flex;
    align-items: center;
    padding: 30px;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.contact-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.phone-card {
    background-color: #2d2d2d;
    color: white;
}

.email-card {
    background-color: #27ae60;
    color: white;
}

.icon-container {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 20px;
}

.phone-card .icon-container {
    background-color: rgba(39, 174, 96, 0.2);
}

.email-card .icon-container {
    background-color: rgba(255, 255, 255, 0.2);
}

.icon {
    font-size: 30px;
    color: white;
}

.contact-info {
    flex: 1;
    background-color: white;
    padding: 25px;
    border-radius: 10px;
}

.contact-info h3 {
    color: #ff6b35;
    font-size: 20px;
    margin-bottom: 5px;
}

.contact-info p {
    color: #333;
    font-size: 16px;
    line-height: 1.5;
    overflow-wrap: break-word;
}

.contact-info a {
    display: block;
    margin-top: 10px;
    color: #27ae60;
    text-decoration: none;
    font-weight: 600;
    transition: color 0.3s ease;
}

.contact-info a:hover {
    color: #ff6b35;
}

/* Responsive styles */
@media (max-width: 768px) {
    .contact-cards {
        flex-direction: column;
    }
    
    .contact-card {
        width: 100%;
    }
}
