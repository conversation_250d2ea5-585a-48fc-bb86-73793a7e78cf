/* Mobile-specific optimizations */
@media (max-width: 768px) {
  /* Touch-friendly elements */
  button,
  .btn,
  .nav-link,
  input[type="submit"],
  input[type="button"],
  .clickable {
    min-height: 44px;
    min-width: 44px;
    padding: 12px 20px;
    touch-action: manipulation;
  }

  /* Improved tap targets */
  a {
    padding: 8px;
    margin: -8px;
  }

  /* Prevent text size adjustment */
  html {
    -webkit-text-size-adjust: 100%;
  }

  /* Optimize scrolling */
  .scroll-container {
    -webkit-overflow-scrolling: touch;
    scroll-behavior: smooth;
  }

  /* Hide scrollbars but keep functionality */
  .hide-scrollbar {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .hide-scrollbar::-webkit-scrollbar {
    display: none;
  }

  /* Mobile navigation */
  .mobile-nav {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: var(--card-bg);
    padding: 0.5rem;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
    z-index: 1000;
  }

  .mobile-nav-items {
    display: flex;
    justify-content: space-around;
    align-items: center;
  }

  .mobile-nav-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-decoration: none;
    color: var(--text-color);
    font-size: 0.75rem;
    padding: 0.5rem;
  }

  .mobile-nav-item i {
    font-size: 1.5rem;
    margin-bottom: 0.25rem;
  }

  /* Mobile menu */
  .mobile-menu {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--card-bg);
    z-index: 1001;
    transform: translateX(-100%);
    transition: transform 0.3s ease-in-out;
  }

  .mobile-menu.active {
    transform: translateX(0);
  }

  .mobile-menu-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    border-bottom: 1px solid var(--border-color);
  }

  .mobile-menu-close {
    padding: 0.5rem;
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
  }

  .mobile-menu-items {
    padding: 1rem;
  }

  .mobile-menu-item {
    display: block;
    padding: 1rem;
    text-decoration: none;
    color: var(--text-color);
    border-bottom: 1px solid var(--border-color);
  }

  /* Mobile forms */
  input[type="text"],
  input[type="email"],
  input[type="password"],
  input[type="number"],
  input[type="tel"],
  input[type="url"],
  input[type="search"],
  textarea,
  select {
    font-size: 16px; /* Prevent zoom on iOS */
    padding: 12px;
    border-radius: 8px;
  }

  /* Mobile tables */
  .responsive-table {
    display: block;
    width: 100%;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }

  /* Mobile cards */
  .card {
    margin: 0.5rem;
    border-radius: 12px;
  }

  /* Mobile images */
  img {
    max-width: 100%;
    height: auto;
  }

  /* Mobile lists */
  ul, ol {
    padding-left: 1.5rem;
  }

  /* Mobile spacing */
  .mobile-p-1 { padding: 0.25rem; }
  .mobile-p-2 { padding: 0.5rem; }
  .mobile-p-3 { padding: 1rem; }
  .mobile-p-4 { padding: 1.5rem; }
  .mobile-p-5 { padding: 2rem; }

  .mobile-m-1 { margin: 0.25rem; }
  .mobile-m-2 { margin: 0.5rem; }
  .mobile-m-3 { margin: 1rem; }
  .mobile-m-4 { margin: 1.5rem; }
  .mobile-m-5 { margin: 2rem; }

  /* Mobile utilities */
  .mobile-hidden {
    display: none !important;
  }

  .mobile-block {
    display: block !important;
  }

  .mobile-flex {
    display: flex !important;
  }

  .mobile-grid {
    display: grid !important;
  }

  /* Mobile gestures */
  .swipe-container {
    touch-action: pan-y pinch-zoom;
  }

  /* Mobile animations */
  .mobile-fade {
    animation: fade 0.3s ease-in-out;
  }

  .mobile-slide-up {
    animation: slideUp 0.3s ease-in-out;
  }

  .mobile-slide-down {
    animation: slideDown 0.3s ease-in-out;
  }

  @keyframes fade {
    from { opacity: 0; }
    to { opacity: 1; }
  }

  @keyframes slideUp {
    from { transform: translateY(100%); }
    to { transform: translateY(0); }
  }

  @keyframes slideDown {
    from { transform: translateY(-100%); }
    to { transform: translateY(0); }
  }
}

/* Small mobile devices */
@media (max-width: 360px) {
  .mobile-nav-item {
    font-size: 0.7rem;
  }

  .mobile-nav-item i {
    font-size: 1.25rem;
  }

  .card {
    margin: 0.25rem;
  }

  .mobile-p-5 { padding: 1.5rem; }
  .mobile-m-5 { margin: 1.5rem; }
}

/* Landscape mode */
@media (max-width: 768px) and (orientation: landscape) {
  .mobile-nav {
    position: fixed;
    top: 0;
    bottom: auto;
    padding: 0.25rem;
  }

  .mobile-nav-items {
    justify-content: center;
    gap: 2rem;
  }

  .mobile-nav-item {
    flex-direction: row;
    gap: 0.5rem;
  }

  .mobile-nav-item i {
    margin-bottom: 0;
  }
}

/* High-DPI screens */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  img {
    image-rendering: -webkit-optimize-contrast;
  }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* Dark mode */
@media (prefers-color-scheme: dark) {
  :root {
    --body-bg: #1a1a1a;
    --card-bg: #2d2d2d;
    --text-color: #ffffff;
    --border-color: #404040;
  }
} 