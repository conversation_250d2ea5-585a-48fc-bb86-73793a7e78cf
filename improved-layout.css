/* Improved Layout System */
.layout {
  display: grid;
  grid-template-columns: var(--sidebar-width) 1fr;
  min-height: 100vh;
}

.sidebar {
  background-color: var(--card-bg);
  padding: 1.5rem;
  border-right: 1px solid var(--border-color);
  position: fixed;
  width: var(--sidebar-width);
  height: 100vh;
  overflow-y: auto;
}

.content {
  margin-left: var(--sidebar-width);
  padding: 2rem;
}

/* Responsive Grid System */
.grid {
  display: grid;
  gap: 1.5rem;
  grid-template-columns: repeat(12, 1fr);
}

.col-1 { grid-column: span 1; }
.col-2 { grid-column: span 2; }
.col-3 { grid-column: span 3; }
.col-4 { grid-column: span 4; }
.col-5 { grid-column: span 5; }
.col-6 { grid-column: span 6; }
.col-7 { grid-column: span 7; }
.col-8 { grid-column: span 8; }
.col-9 { grid-column: span 9; }
.col-10 { grid-column: span 10; }
.col-11 { grid-column: span 11; }
.col-12 { grid-column: span 12; }

/* Responsive Design */
@media (max-width: 1200px) {
  .grid {
    gap: 1rem;
  }
}

@media (max-width: 992px) {
  .layout {
    grid-template-columns: 1fr;
  }
  
  .sidebar {
    position: relative;
    width: 100%;
    height: auto;
    border-right: none;
    border-bottom: 1px solid var(--border-color);
  }
  
  .content {
    margin-left: 0;
    padding: 1.5rem;
  }
  
  .col-md-1 { grid-column: span 1; }
  .col-md-2 { grid-column: span 2; }
  .col-md-3 { grid-column: span 3; }
  .col-md-4 { grid-column: span 4; }
  .col-md-6 { grid-column: span 6; }
  .col-md-8 { grid-column: span 8; }
  .col-md-9 { grid-column: span 9; }
  .col-md-12 { grid-column: span 12; }
}

@media (max-width: 768px) {
  .grid {
    gap: 0.75rem;
  }
  
  .content {
    padding: 1rem;
  }
  
  .col-sm-1 { grid-column: span 1; }
  .col-sm-2 { grid-column: span 2; }
  .col-sm-3 { grid-column: span 3; }
  .col-sm-4 { grid-column: span 4; }
  .col-sm-6 { grid-column: span 6; }
  .col-sm-12 { grid-column: span 12; }
}

@media (max-width: 576px) {
  .grid {
    gap: 0.5rem;
  }
  
  .content {
    padding: 0.75rem;
  }
  
  .col-xs-1 { grid-column: span 1; }
  .col-xs-2 { grid-column: span 2; }
  .col-xs-3 { grid-column: span 3; }
  .col-xs-4 { grid-column: span 4; }
  .col-xs-6 { grid-column: span 6; }
  .col-xs-12 { grid-column: span 12; }
}

/* Flexbox Utilities */
.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.flex-wrap {
  flex-wrap: wrap;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.gap-1 { gap: 0.25rem; }
.gap-2 { gap: 0.5rem; }
.gap-3 { gap: 1rem; }
.gap-4 { gap: 1.5rem; }
.gap-5 { gap: 3rem; }

/* Spacing Utilities */
.space-y-1 > * + * { margin-top: 0.25rem; }
.space-y-2 > * + * { margin-top: 0.5rem; }
.space-y-3 > * + * { margin-top: 1rem; }
.space-y-4 > * + * { margin-top: 1.5rem; }
.space-y-5 > * + * { margin-top: 3rem; }

.space-x-1 > * + * { margin-left: 0.25rem; }
.space-x-2 > * + * { margin-left: 0.5rem; }
.space-x-3 > * + * { margin-left: 1rem; }
.space-x-4 > * + * { margin-left: 1.5rem; }
.space-x-5 > * + * { margin-left: 3rem; }

/* Responsive Spacing */
@media (max-width: 768px) {
  .space-y-4 > * + * { margin-top: 1rem; }
  .space-y-5 > * + * { margin-top: 1.5rem; }
  
  .space-x-4 > * + * { margin-left: 1rem; }
  .space-x-5 > * + * { margin-left: 1.5rem; }
}

/* Container Queries */
@container (min-width: 400px) {
  .container-adaptive {
    padding: 1rem;
  }
}

@container (min-width: 600px) {
  .container-adaptive {
    padding: 1.5rem;
  }
}

@container (min-width: 800px) {
  .container-adaptive {
    padding: 2rem;
  }
}

/* Aspect Ratio */
.aspect-video {
  aspect-ratio: 16 / 9;
}

.aspect-square {
  aspect-ratio: 1 / 1;
}

/* Object Fit */
.object-cover {
  object-fit: cover;
}

.object-contain {
  object-fit: contain;
}

/* Responsive Images */
.img-fluid {
  max-width: 100%;
  height: auto;
}

/* Responsive Typography */
@media (max-width: 768px) {
  h1 { font-size: 2rem; }
  h2 { font-size: 1.75rem; }
  h3 { font-size: 1.5rem; }
  h4 { font-size: 1.25rem; }
  h5 { font-size: 1.125rem; }
  h6 { font-size: 1rem; }
}

@media (max-width: 576px) {
  h1 { font-size: 1.75rem; }
  h2 { font-size: 1.5rem; }
  h3 { font-size: 1.25rem; }
  h4 { font-size: 1.125rem; }
  h5 { font-size: 1rem; }
  h6 { font-size: 0.875rem; }
} 