/* Base styles */
:root {
  --primary-color: #007bff;
  --secondary-color: #6c757d;
  --success-color: #28a745;
  --danger-color: #dc3545;
  --warning-color: #ffc107;
  --info-color: #17a2b8;
  --light-color: #f8f9fa;
  --dark-color: #343a40;
  --body-bg: #f5f5f5;
  --card-bg: #ffffff;
  --text-color: #333333;
  --border-color: #dee2e6;
  --header-height: 60px;
  --footer-height: 60px;
  --sidebar-width: 250px;
  --border-radius: 8px;
  --box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  --transition-speed: 0.3s;
}

/* Reset and base styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  line-height: 1.6;
  color: var(--text-color);
  background-color: var(--body-bg);
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Layout */
.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 15px;
}

/* Header */
.header {
  background-color: var(--card-bg);
  box-shadow: var(--box-shadow);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  height: var(--header-height);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100%;
  padding: 0 15px;
}

/* Navigation */
.nav {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.nav-link {
  color: var(--text-color);
  text-decoration: none;
  padding: 0.5rem 1rem;
  border-radius: var(--border-radius);
  transition: background-color var(--transition-speed);
}

.nav-link:hover {
  background-color: var(--light-color);
}

/* Main content */
.main-content {
  flex: 1;
  margin-top: var(--header-height);
  padding: 2rem 0;
}

/* Cards */
.card {
  background-color: var(--card-bg);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  padding: 1.5rem;
  margin-bottom: 1.5rem;
}

/* Forms */
.form-group {
  margin-bottom: 1rem;
}

.form-label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
}

.form-control {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  transition: border-color var(--transition-speed);
}

.form-control:focus {
  outline: none;
  border-color: var(--primary-color);
}

/* Buttons */
.btn {
  display: inline-block;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: all var(--transition-speed);
  text-decoration: none;
  font-weight: 500;
}

.btn-primary {
  background-color: var(--primary-color);
  color: white;
}

.btn-primary:hover {
  background-color: darken(var(--primary-color), 10%);
}

/* Footer */
.footer {
  background-color: var(--card-bg);
  padding: 1.5rem 0;
  margin-top: auto;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .container {
    max-width: 960px;
  }
}

@media (max-width: 992px) {
  .container {
    max-width: 720px;
  }
  
  .header-content {
    padding: 0 10px;
  }
  
  .nav {
    gap: 0.5rem;
  }
  
  .nav-link {
    padding: 0.5rem;
  }
}

@media (max-width: 768px) {
  .container {
    max-width: 540px;
  }
  
  .header {
    height: auto;
    position: relative;
  }
  
  .header-content {
    flex-direction: column;
    padding: 1rem;
  }
  
  .nav {
    flex-direction: column;
    width: 100%;
    margin-top: 1rem;
  }
  
  .nav-link {
    width: 100%;
    text-align: center;
  }
  
  .main-content {
    margin-top: 0;
    padding: 1rem 0;
  }
  
  .card {
    padding: 1rem;
  }
  
  .btn {
    width: 100%;
    text-align: center;
  }
}

@media (max-width: 576px) {
  .container {
    padding: 0 10px;
  }
  
  .header-content {
    padding: 0.5rem;
  }
  
  .card {
    padding: 0.75rem;
  }
  
  .form-control {
    padding: 0.5rem;
  }
  
  .btn {
    padding: 0.5rem 1rem;
  }
}

/* Utility classes */
.text-center { text-align: center; }
.mt-1 { margin-top: 0.25rem; }
.mt-2 { margin-top: 0.5rem; }
.mt-3 { margin-top: 1rem; }
.mt-4 { margin-top: 1.5rem; }
.mt-5 { margin-top: 3rem; }
.mb-1 { margin-bottom: 0.25rem; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-3 { margin-bottom: 1rem; }
.mb-4 { margin-bottom: 1.5rem; }
.mb-5 { margin-bottom: 3rem; }
.p-1 { padding: 0.25rem; }
.p-2 { padding: 0.5rem; }
.p-3 { padding: 1rem; }
.p-4 { padding: 1.5rem; }
.p-5 { padding: 3rem; }

/* Grid system */
.row {
  display: flex;
  flex-wrap: wrap;
  margin: -15px;
}

.col {
  flex: 1;
  padding: 15px;
}

@media (max-width: 768px) {
  .row {
    flex-direction: column;
  }
  
  .col {
    width: 100%;
  }
}

/* Accessibility */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Print styles */
@media print {
  .header,
  .footer,
  .btn {
    display: none;
  }
  
  body {
    background: white;
  }
  
  .container {
    width: 100%;
    max-width: none;
    padding: 0;
  }
  
  .card {
    box-shadow: none;
    border: 1px solid #ddd;
  }
} 